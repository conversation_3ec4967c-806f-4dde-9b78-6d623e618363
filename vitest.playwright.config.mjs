import { defineConfig } from 'vitest/config';

// Check if running in CI environment
const isCI = process.env.CI === 'true';

export default defineConfig({
  test: {
    environment: 'node', // Use node environment for Playwright tests
    globals: true,
    // Each worker creates its own Electron instance, no shared setup needed
    globalTeardown: './e2e/global-teardown.ts',
    include: [
      'e2e/**/*.e2e.ts', // All e2e tests now use Playwright/Electron setup
    ],
    exclude: [
      'tests/**/*',                 // Exclude unit tests
    ],
    testTimeout: isCI ? 180000 : 120000, // 3 minutes for CI, 2 minutes for local
    hookTimeout: isCI ? 90000 : 60000,   // 1.5 minutes for CI, 1 minute for local
    // Adjust concurrency for CI vs local development
    fileParallelism: true,
    maxConcurrency: isCI ? 1 : 2, // Single worker in CI for stability, 2 workers locally
    // Run tests in threads with worker isolation
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: isCI, // Single thread in CI for better stability
        isolate: true       // Isolate workers for better test isolation
      }
    },
    // CI-specific reporter configuration
    reporter: isCI ? ['verbose', 'junit'] : 'default',
    outputFile: isCI ? {
      junit: './test-results/junit.xml'
    } : undefined
  },
  define: {
    global: 'globalThis'
  }
});
