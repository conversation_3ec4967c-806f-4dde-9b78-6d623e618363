import type { Page } from 'playwright';

/**
 * Settings helpers for e2e tests
 * Simple and fast approach using commands
 */

/**
 * Open Ghost Sync plugin settings
 * Uses command palette to open settings, then clicks Ghost Sync
 */
export async function openGhostSyncSettings(page: Page): Promise<void> {
  // Close any open modals first
  await page.keyboard.press('Escape');
  
  // Execute "Open Settings" command
  await page.keyboard.press('Meta+P');
  await page.waitForSelector('.prompt-input');
  await page.fill('.prompt-input', 'Open Settings');
  await page.keyboard.press('Enter');

  // Wait for settings modal
  await page.waitForSelector('.modal-container', { timeout: 5000 });

  // Navigate to Community plugins
  await page.click('text=Community plugins');
  await page.waitForTimeout(200);

  // Click on Ghost Sync plugin
  await page.click('text=Ghost Sync');

  // Wait for Ghost Sync settings to load
  await page.waitForSelector('text=Ghost Sync Settings', { timeout: 5000 });
}

/**
 * Close settings modal
 */
export async function closeSettings(page: Page): Promise<void> {
  await page.keyboard.press('Escape');
  await page.waitForTimeout(100);
}

/**
 * Fill a setting input field
 */
export async function fillSettingInput(page: Page, placeholder: string, value: string): Promise<void> {
  const input = page.locator(`input[placeholder="${placeholder}"]`);
  await input.clear();
  await input.fill(value);
  await page.waitForTimeout(100);
}
