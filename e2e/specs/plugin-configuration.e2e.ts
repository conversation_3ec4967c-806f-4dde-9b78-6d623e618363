import {
  getSharedTestContext,
  resetSharedTestContext,
  cleanupTestState,
  type SharedTestContext
} from '../helpers/shared-context';
import { openGhostSyncSettings, closeSettings, fillSettingInput } from '../helpers/settings-helpers';
import { test, beforeAll, beforeEach, afterEach, afterAll, expect } from 'vitest';

describe("Plugin Configuration", () => {
  let context: SharedTestContext;

  beforeAll(async () => { context = await getSharedTestContext(); });

  beforeEach(async () => { await resetSharedTestContext(); });

  afterEach(async () => { await cleanupTestState(); });

  afterAll(async () => {
    // Force cleanup of any remaining processes
    try {
      if (context?.electronApp) {
        await context.electronApp.close();
      }
    } catch (error) {
      console.log('⚠️ Error during afterAll cleanup:', error.message);
    }
  });

  test("should open plugin settings and configure basic options", async () => {
    await openGhostSyncSettings(context.page);

    await fillSettingInput(context.page, 'https://your-site.ghost.io', 'https://test-ghost-site.com');
    await fillSettingInput(context.page, 'id:secret', 'test123:secret456');
    await fillSettingInput(context.page, 'articles', 'my-articles');

    const verboseToggle = context.page.locator('input[type="checkbox"]').filter({ hasText: 'Verbose' }).or(
      context.page.locator('.checkbox-container').filter({ hasText: 'Verbose' }).locator('input').or(
        context.page.locator('[data-setting="verbose"]').or(
          context.page.locator('text=Verbose').locator('..').locator('input[type="checkbox"]')
        )
      )
    );

    const verboseExists = await verboseToggle.count() > 0;
    if (verboseExists) {
      await verboseToggle.check();
    }

    await closeSettings(context.page);

    const pluginSettings = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin ? plugin.settings : null;
    });

    expect(pluginSettings).toBeTruthy();
    expect(pluginSettings.ghostUrl).toBe('https://test-ghost-site.com');
    expect(pluginSettings.ghostAdminApiKey).toBe('test123:secret456');
    expect(pluginSettings.articlesDir).toBe('my-articles');

    if (verboseExists) {
      expect(pluginSettings.verbose).toBe(true);
    }
  });

  test("should persist plugin settings after restart", async () => {
    await openGhostSyncSettings(context.page);

    const testUrl = 'https://persistent-test.ghost.io';
    const testApiKey = 'persist123:secret789';
    const testDir = 'persistent-articles';

    await fillSettingInput(context.page, 'https://your-site.ghost.io', testUrl);
    await fillSettingInput(context.page, 'id:secret', testApiKey);
    await fillSettingInput(context.page, 'articles', testDir);

    await closeSettings(context.page);

    await context.page.evaluate(async () => {
      const app = (window as any).app;
      await app.plugins.disablePlugin('ghost-sync');
      await app.plugins.enablePlugin('ghost-sync');
    });

    await context.page.waitForTimeout(1000);

    const persistedSettings = await context.page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin ? plugin.settings : null;
    });

    expect(persistedSettings).toBeTruthy();
    expect(persistedSettings.ghostUrl).toBe(testUrl);
    expect(persistedSettings.ghostAdminApiKey).toBe(testApiKey);
    expect(persistedSettings.articlesDir).toBe(testDir);
  });

  test("should validate Ghost API key format", async () => {
    await openGhostSyncSettings(context.page);

    const apiKeyInput = context.page.locator('input[placeholder="id:secret"]');
    await apiKeyInput.clear();
    await apiKeyInput.fill('invalid-key-format');

    const currentValue = await apiKeyInput.inputValue();
    expect(currentValue).toBe('invalid-key-format');

    await apiKeyInput.clear();
    await apiKeyInput.fill('1234567890abcdef1234567890:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef');

    const validValue = await apiKeyInput.inputValue();
    expect(validValue).toBe('1234567890abcdef1234567890:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef');
  });

  // FIXME: this only passes when run in isolation
  // test("should update articles directory and affect plugin behavior", async () => {
  //   await openGhostSyncSettings(context.page);

  //   const customDir = 'custom-posts';
  //   await fillSettingInput(context.page, 'articles', customDir);

  //   await closeSettings(context.page);

  //   const appAdapterDir = await context.page.evaluate(() => {
  //     const plugin = (window as any).app.plugins.plugins['ghost-sync'];
  //     return plugin && plugin.appAdapter ? plugin.appAdapter.articlesDir : null;
  //   });

  //   expect(appAdapterDir).toBe(customDir);
  // });
});
