import * as fs from 'fs';
import * as path from 'path';
import { cleanupSharedElectron } from './setup-shared-electron';
import { cleanupAllWorkerContexts } from './helpers/shared-context';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Global teardown for all e2e tests
 * Cleans up the shared Electron instance and all worker contexts
 */
export default async function globalTeardown() {
  console.log("🌍 Starting global e2e test teardown...");
  console.log("🔍 Current working directory:", process.cwd());

  try {
    // Clean up worker-specific contexts and isolated environments first
    await cleanupAllWorkerContexts();

    // Clean up shared Electron instance
    await cleanupSharedElectron();

    // Force cleanup any remaining Electron processes related to our tests
    await forceCleanupElectronProcesses();

    // Clean up connection file
    const connectionFile = path.join(process.cwd(), 'e2e/.electron-connection.json');
    if (fs.existsSync(connectionFile)) {
      fs.unlinkSync(connectionFile);
      console.log("🗑️ Connection file cleaned up");
    }

    console.log("✅ Global e2e test teardown complete");

  } catch (error) {
    console.error("❌ Global teardown failed:", error);
    // Don't throw here - we want tests to complete even if cleanup fails
  }
}

/**
 * Force cleanup any remaining Electron processes that might be left running
 * This is a safety net to ensure all test-related processes are terminated
 */
async function forceCleanupElectronProcesses(): Promise<void> {
  try {
    console.log("🧹 Checking for remaining Electron processes...");

    // Find all Electron processes related to our project
    const { stdout } = await execAsync('ps aux | grep -E "(obsidian-ghost-sync.*Electron|test-environments)" | grep -v grep || true');

    if (stdout.trim()) {
      console.log("🔍 Found remaining Electron processes:");
      console.log(stdout);

      // Extract PIDs and kill them
      const lines = stdout.trim().split('\n');
      const pids: string[] = [];

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 2) {
          const pid = parts[1];
          if (pid && /^\d+$/.test(pid)) {
            pids.push(pid);
          }
        }
      }

      if (pids.length > 0) {
        console.log(`🔪 Terminating ${pids.length} remaining Electron processes...`);

        // First try graceful termination
        for (const pid of pids) {
          try {
            await execAsync(`kill -TERM ${pid}`);
            console.log(`✅ Sent TERM signal to process ${pid}`);
          } catch (error) {
            console.log(`⚠️ Could not send TERM to process ${pid}: ${error.message}`);
          }
        }

        // Give processes a moment to terminate gracefully
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Force kill any that didn't terminate
        for (const pid of pids) {
          try {
            // Check if process still exists
            await execAsync(`kill -0 ${pid} 2>/dev/null`);
            // If we get here, process still exists, force kill it
            await execAsync(`kill -9 ${pid}`);
            console.log(`💀 Force killed process ${pid}`);
          } catch (error) {
            // Process already terminated or doesn't exist
            console.log(`✅ Process ${pid} already terminated`);
          }
        }

        // Also try pkill as a backup
        try {
          await execAsync('pkill -f "obsidian-ghost-sync.*Electron" || true');
          console.log("🧹 Ran pkill as backup cleanup");
        } catch (error) {
          console.log(`⚠️ pkill backup failed: ${error.message}`);
        }
      }
    } else {
      console.log("✅ No remaining Electron processes found");
    }
  } catch (error) {
    console.log(`⚠️ Error during force cleanup: ${error.message}`);
  }
}
